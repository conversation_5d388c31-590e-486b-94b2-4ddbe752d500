#!/usr/bin/env python3
import boto3
import logging
from botocore.exceptions import ClientError

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("backup-vault-lock-checker")

def list_backup_vault_locks(profile_name="astra-iac"):
    """
    List all backup vaults with Vault Lock enabled in the AWS account

    Args:
        profile_name: AWS profile name to use
    """
    # Create a session with the specified profile
    session = boto3.Session(profile_name=profile_name)
    
    # Create a backup client
    backup_client = session.client('backup')
    
    try:
        # List all backup vaults
        paginator = backup_client.get_paginator('list_backup_vaults')
        locked_vaults = []
        
        # Iterate through all backup vaults
        for page in paginator.paginate():
            for vault in page.get('BackupVaultList', []):
                vault_name = vault['BackupVaultName']
                
                try:
                    # Get the vault details including lock configuration
                    vault_details = backup_client.describe_backup_vault(
                        BackupVaultName=vault_name
                    )

                    # Check if the vault has Vault Lock enabled
                    if vault_details.get('Locked', False):
                        locked_vaults.append({
                            'VaultName': vault_name,
                            'Locked': vault_details.get('Locked', False),
                            'MinRetentionDays': vault_details.get('MinRetentionDays', 'N/A'),
                            'MaxRetentionDays': vault_details.get('MaxRetentionDays', 'N/A'),
                            'LockDate': vault_details.get('LockDate', 'N/A'),
                            'VaultType': vault_details.get('VaultType', 'N/A'),
                            'VaultState': vault_details.get('VaultState', 'N/A')
                        })

                except ClientError as e:
                    if e.response['Error']['Code'] == 'ResourceNotFoundException':
                        # Vault doesn't exist
                        continue
                    else:
                        logger.error(f"Error getting vault details for {vault_name}: {str(e)}")
        
        # Print results
        if locked_vaults:
            logger.info(f"Found {len(locked_vaults)} vaults with Vault Lock enabled:")
            for vault in locked_vaults:
                logger.info(f"Vault: {vault['VaultName']}")
                logger.info(f"  Locked: {vault['Locked']}")
                logger.info(f"  Vault Type: {vault['VaultType']}")
                logger.info(f"  Vault State: {vault['VaultState']}")
                logger.info(f"  Min Retention Days: {vault['MinRetentionDays']}")
                logger.info(f"  Max Retention Days: {vault['MaxRetentionDays']}")
                logger.info(f"  Lock Date: {vault['LockDate']}")
                logger.info("---")
        else:
            logger.info("No backup vaults with Vault Lock found.")
            
        return locked_vaults
            
    except ClientError as e:
        logger.error(f"Error listing backup vaults: {str(e)}")
        return []

if __name__ == "__main__":
    list_backup_vault_locks()