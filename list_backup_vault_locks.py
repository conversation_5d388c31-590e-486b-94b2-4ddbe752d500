#!/usr/bin/env python3
import boto3
import logging
from botocore.exceptions import ClientError

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("backup-vault-lock-checker")

def list_backup_vault_locks(profile_name="astra-iac"):
    """
    List all backup vaults with Compliance Lock in the AWS account
    
    Args:
        profile_name: AWS profile name to use
    """
    # Create a session with the specified profile
    session = boto3.Session(profile_name=profile_name)
    
    # Create a backup client
    backup_client = session.client('backup')
    
    try:
        # List all backup vaults
        paginator = backup_client.get_paginator('list_backup_vaults')
        locked_vaults = []
        
        # Iterate through all backup vaults
        for page in paginator.paginate():
            for vault in page.get('BackupVaultList', []):
                vault_name = vault['BackupVaultName']
                
                try:
                    # Get the vault lock configuration
                    lock_config = backup_client.get_backup_vault_lock_configuration(
                        BackupVaultName=vault_name
                    )
                    
                    # Check if the vault has a Compliance lock
                    if 'LockState' in lock_config and lock_config['LockState'] == 'COMPLIANCE_MODE':
                        locked_vaults.append({
                            'VaultName': vault_name,
                            'LockState': lock_config['LockState'],
                            'MinRetentionDays': lock_config.get('MinRetentionDays', 'N/A'),
                            'MaxRetentionDays': lock_config.get('MaxRetentionDays', 'N/A'),
                            'ChangeableForDays': lock_config.get('ChangeableForDays', 'N/A'),
                            'LockDate': lock_config.get('LockDate', 'N/A')
                        })
                        
                except ClientError as e:
                    if e.response['Error']['Code'] == 'ResourceNotFoundException':
                        # Vault doesn't have lock configuration
                        continue
                    else:
                        logger.error(f"Error getting lock configuration for vault {vault_name}: {str(e)}")
        
        # Print results
        if locked_vaults:
            logger.info(f"Found {len(locked_vaults)} vaults with Compliance Lock:")
            for vault in locked_vaults:
                logger.info(f"Vault: {vault['VaultName']}")
                logger.info(f"  Lock State: {vault['LockState']}")
                logger.info(f"  Min Retention Days: {vault['MinRetentionDays']}")
                logger.info(f"  Max Retention Days: {vault['MaxRetentionDays']}")
                logger.info(f"  Changeable For Days: {vault['ChangeableForDays']}")
                logger.info(f"  Lock Date: {vault['LockDate']}")
                logger.info("---")
        else:
            logger.info("No backup vaults with Compliance Lock found.")
            
        return locked_vaults
            
    except ClientError as e:
        logger.error(f"Error listing backup vaults: {str(e)}")
        return []

if __name__ == "__main__":
    list_backup_vault_locks()